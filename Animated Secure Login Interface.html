<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@300;400;500;600&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: { geist: ['Geist', 'sans-serif'] },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'login-pulse': 'loginPulse 4s ease-in-out infinite',
                        'field-glow': 'fieldGlow 2s ease-in-out infinite',
                        'button-shine': 'buttonShine 3s ease-in-out infinite',
                    },
                    keyframes: {
                        float: { 
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        loginPulse: {
                            '0%, 100%': { transform: 'scale(1)', opacity: 0.8 },
                            '50%': { transform: 'scale(1.02)', opacity: 1 }
                        },
                        fieldGlow: {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(79, 70, 229, 0.3)' },
                            '50%': { boxShadow: '0 0 30px rgba(139, 92, 246, 0.5)' }
                        },
                        buttonShine: {
                            '0%, 100%': { backgroundPosition: '-200% center' },
                            '50%': { backgroundPosition: '200% center' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        #aurora-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }
        .gradient-border {
            position: relative;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
        }
        .gradient-border::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: linear-gradient(135deg, #4f46e5, #3b82f6, #8b5cf6, #f59e0b);
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
        }
        .card-border {
            background: rgba(79, 70, 229, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            box-shadow: 
                inset 0 0 30px rgba(79, 70, 229, 0.1),
                inset 0 0 60px rgba(59, 130, 246, 0.05),
                0 0 50px rgba(139, 92, 246, 0.2);
        }
        .input-field {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(79, 70, 229, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .input-field:focus {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(139, 92, 246, 0.6);
            box-shadow: 0 0 25px rgba(139, 92, 246, 0.4);
        }
        .login-button {
            background: linear-gradient(135deg, #4f46e5, #8b5cf6);
            background-size: 200% 100%;
            animation: buttonShine 3s ease-in-out infinite;
        }
    </style>
</head>
<body class="bg-zinc-900 text-white overflow-hidden font-geist">
    <canvas id="aurora-canvas"></canvas>
    
    <!-- Login Card -->
    <div class="fixed inset-0 flex items-center justify-center p-4 z-10">
        <div class="w-full relative max-w-sm">
            <!-- Card content -->
            <div class="relative card-border overflow-hidden rounded-2xl flex flex-col animate-float">
                <!-- Login Icon Preview -->
                <div class="p-6 pb-0 flex justify-center relative">
                    <div class="w-full h-32 rounded-xl gradient-border overflow-hidden relative animate-login-pulse">
                        <!-- Abstract login visual -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="relative">
                                <!-- User avatar circle -->
                                <div class="w-16 h-16 rounded-full glass border-2 border-indigo-400/30 flex items-center justify-center mb-4">
                            <img src="/login-logo.png" alt="Logo" class="w-12 h-12">
                        </div>
                                
                                <!-- Floating security elements -->
                                <div class="absolute -top-2 -right-2">
                                    <div class="w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                                </div>
                                <div class="absolute -bottom-1 -left-2">
                                    <div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
                                </div>
                                <div class="absolute top-1/2 -right-4">
                                    <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 1s"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Security badge -->
                        <div class="absolute bottom-2 right-2">
                            <div class="flex items-center glass px-2 py-1 rounded-full border border-green-400/20">
                                <svg class="w-3 h-3 text-green-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-xs text-green-300">Secure</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Login Form -->
                <div class="p-6">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-white mb-2">Sign In</h3>
                        <p class="text-white/60 text-sm">Access your secure account</p>
                    </div>
                    
                    <form class="space-y-4">
                        <!-- Email Field -->
                        <div class="relative">
                            <label class="block text-sm font-medium text-white/80 mb-2">Email</label>
                            <div class="relative">
                                <input type="email" 
                                       class="input-field w-full px-4 py-3 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-0"
                                       placeholder="Enter your email">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <svg class="w-4 h-4 text-white/40" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Password Field -->
                        <div class="relative">
                            <label class="block text-sm font-medium text-white/80 mb-2">Password</label>
                            <div class="relative">
                                <input type="password" 
                                       class="input-field w-full px-4 py-3 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-0"
                                       placeholder="Enter your password">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <svg class="w-4 h-4 text-white/40" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Remember & Forgot -->
                        <div class="flex items-center justify-between text-sm">
                            <label class="flex items-center">
                                <input type="checkbox" class="sr-only">
                                <div class="w-4 h-4 border-2 border-indigo-400/50 rounded glass flex items-center justify-center">
                                    <svg class="w-3 h-3 text-indigo-400 hidden" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <span class="ml-2 text-white/60">Remember me</span>
                            </label>
                            <a href="#" class="text-indigo-400 hover:text-indigo-300 transition">Forgot password?</a>
                        </div>
                        
                        <!-- Login Button -->
                        <button type="submit" class="w-full login-button text-white font-medium py-3 px-4 rounded-lg transition hover:shadow-lg hover:shadow-purple-500/25 focus:outline-none focus:ring-2 focus:ring-purple-500/50">
                            Sign In
                        </button>
                        
                        <!-- Or continue with -->
                        <div class="text-center my-6">
                            <span class="text-white/60 text-sm">or continue with</span>
                        </div>
                        
                        <!-- Social Login -->
                        <div class="grid grid-cols-2 gap-3">
                            <button class="glass flex items-center justify-center px-4 py-2 border border-white/20 rounded-lg hover:bg-white/10 transition text-sm text-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500/50" onclick="window.open('https://work.weixin.qq.com/kfid/kfc605d7e78dd00133d', '_blank')">
                                联系客服
                            </button>
                            <button class="glass flex items-center justify-center px-4 py-2 border border-white/20 rounded-lg hover:bg-white/10 transition text-sm text-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500/50">
                                设备码
                            </button>
                        </div>
                        
                        <!-- Sign up link -->
                        <div class="text-center mt-6">
                            <span class="text-white/60 text-sm">Don't have an account? </span>
                            <a href="#" class="text-indigo-400 hover:text-indigo-300 transition text-sm font-medium">Sign up</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('aurora-canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

        if (!gl) {
            console.error('WebGL not supported');
        }

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            gl.viewport(0, 0, canvas.width, canvas.height);
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        const vertexShaderSource = `
            attribute vec2 a_position;
            void main() {
                gl_Position = vec4(a_position, 0.0, 1.0);
            }
        `;

        const fragmentShaderSource = `
            precision mediump float;
            uniform float u_time;
            uniform vec2 u_resolution;

            vec3 aurora(vec2 uv, float time) {
                vec2 p = uv - 0.5;
                p.y += 0.3;
                
                float wave1 = sin(p.x * 3.0 + time * 0.5) * 0.08;
                float wave2 = sin(p.x * 5.0 + time * 0.7 + sin(time * 0.3) * 2.0) * 0.04;
                float wave3 = sin(p.x * 7.0 + time * 1.1 + cos(time * 0.4) * 1.5) * 0.025;
                float wave4 = sin(p.x * 2.0 + time * 0.3 + sin(time * 0.6) * 3.0) * 0.06;
                
                float y = p.y - wave1 - wave2 - wave3 - wave4;
                
                float intensity1 = exp(-abs(y) * 16.0) * 0.375;
                float intensity2 = exp(-abs(y + 0.1) * 24.0) * 0.3;
                float intensity3 = exp(-abs(y - 0.05) * 30.0) * 0.225;
                
                vec3 color1 = vec3(0.2, 0.8, 0.9) * intensity1;
                vec3 color2 = vec3(0.5, 0.3, 0.9) * intensity2;
                vec3 color3 = vec3(0.1, 0.9, 0.6) * intensity3;
                
                return color1 + color2 + color3;
            }

            vec3 secondaryAurora(vec2 uv, float time) {
                vec2 p = uv - 0.5;
                p.y += 0.1;
                
                float wave1 = sin(p.x * 2.0 + time * 0.3 + sin(time * 0.2) * 2.5) * 0.06;
                float wave2 = cos(p.x * 4.0 + time * 0.5 + cos(time * 0.35) * 1.8) * 0.03;
                float y = p.y - wave1 - wave2;
                
                float intensity = exp(-abs(y) * 12.0) * 0.225;
                return vec3(0.8, 0.2, 0.7) * intensity;
            }

            vec3 tertiaryAurora(vec2 uv, float time) {
                vec2 p = uv - 0.5;
                p.y -= 0.2;
                
                float wave1 = sin(p.x * 1.5 + time * 0.4 + sin(time * 0.25) * 3.0) * 0.07;
                float wave2 = cos(p.x * 3.5 + time * 0.6 + cos(time * 0.45) * 2.2) * 0.035;
                float y = p.y - wave1 - wave2;
                
                float intensity = exp(-abs(y) * 18.0) * 0.18;
                return vec3(0.3, 0.9, 0.5) * intensity;
            }

            float noise(vec2 p) {
                return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453);
            }

            void main() {
                vec2 uv = gl_FragCoord.xy / u_resolution.xy;
                
                vec3 color = vec3(0.03, 0.03, 0.075);
                
                color += aurora(uv, u_time);
                color += secondaryAurora(uv, u_time + 3.0);
                color += tertiaryAurora(uv, u_time + 1.5);
                
                vec2 starUv = uv * 120.0;
                vec2 starId = floor(starUv);
                vec2 starFract = fract(starUv);
                
                float star = noise(starId);
                if (star > 0.985) {
                    float starBrightness = (sin(u_time * 1.5 + star * 8.0) * 0.3 + 0.4) * 0.75;
                    float starDist = length(starFract - 0.5);
                    if (starDist < 0.03) {
                        color += vec3(0.8, 0.9, 1.0) * (1.0 - starDist * 30.0) * starBrightness;
                    }
                }
                
                float glow = 1.0 - length(uv - 0.5) * 0.6;
                color += vec3(0.075, 0.15, 0.225) * glow * 0.225;
                
                gl_FragColor = vec4(color, 1.0);
            }
        `;

        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error('Shader compilation error:', gl.getShaderInfoLog(shader));
                gl.deleteShader(shader);
                return null;
            }
            
            return shader;
        }

        const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
        const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);

        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            console.error('Program linking error:', gl.getProgramInfoLog(program));
        }

        const positionAttributeLocation = gl.getAttribLocation(program, 'a_position');
        const timeUniformLocation = gl.getUniformLocation(program, 'u_time');
        const resolutionUniformLocation = gl.getUniformLocation(program, 'u_resolution');

        const positionBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        const positions = [
            -1, -1,
             1, -1,
            -1,  1,
            -1,  1,
             1, -1,
             1,  1,
        ];
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);

        function render(time) {
            time *= 0.001;

            gl.clearColor(0, 0, 0, 1);
            gl.clear(gl.COLOR_BUFFER_BIT);

            gl.useProgram(program);

            gl.enableVertexAttribArray(positionAttributeLocation);
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.vertexAttribPointer(positionAttributeLocation, 2, gl.FLOAT, false, 0, 0);

            gl.uniform1f(timeUniformLocation, time);
            gl.uniform2f(resolutionUniformLocation, canvas.width, canvas.height);

            gl.drawArrays(gl.TRIANGLES, 0, 6);

            requestAnimationFrame(render);
        }

        requestAnimationFrame(render);

        // Interactive elements
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.classList.add('animate-field-glow');
            });
            input.addEventListener('blur', function() {
                this.classList.remove('animate-field-glow');
            });
        });
    </script>
</body>
</html>