import React, { useState, useEffect, useRef } from 'react';
import DeviceIdGenerator from '../utils/deviceId';

interface DeviceCodeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DeviceCodeModal: React.FC<DeviceCodeModalProps> = ({ isOpen, onClose }) => {
  const [deviceId, setDeviceId] = useState<string>('');
  const [copied, setCopied] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const isMountedRef = useRef(true);
  
  useEffect(() => {
    isMountedRef.current = true;
    if (isOpen) {
      console.log('DeviceCodeModal opened, generating device code...');
      generateDeviceCode();
      
      // 添加键盘事件监听
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          console.log('ESC key pressed, closing modal');
          onClose();
        }
      };
      
      document.addEventListener('keydown', handleKeyDown);
      
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    } else {
      console.log('DeviceCodeModal closed');
      // 重置状态
      setDeviceId('');
      setCopied(false);
      setIsGenerating(false);
    }
    
    return () => {
      isMountedRef.current = false;
    };
  }, [isOpen, onClose]);
  
  const generateDeviceCode = async () => {
    console.log('Starting device code generation...');
    if (!isMountedRef.current) return;
    
    setIsGenerating(true);
    try {
      // 添加一点延迟来显示生成动画
      await new Promise(resolve => setTimeout(resolve, 800));
      
      if (!isMountedRef.current) return;
      
      console.log('Calling DeviceIdGenerator.generateDeviceId()...');
      const id = DeviceIdGenerator.generateDeviceId();
      console.log('Generated device ID:', id);
      
      if (isMountedRef.current) {
        setDeviceId(id);
        console.log('Device ID set successfully');
      }
    } catch (error) {
      console.error('生成设备码失败:', error);
      if (isMountedRef.current) {
        setDeviceId('生成失败，请重试');
      }
    } finally {
      if (isMountedRef.current) {
        setIsGenerating(false);
        console.log('Device code generation completed');
      }
    }
  };
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(deviceId);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
      // 降级方案：选择文本
      const textArea = document.createElement('textarea');
      textArea.value = deviceId;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (fallbackErr) {
        console.error('降级复制也失败:', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };
  
  const handleRefresh = () => {
    if (!isMountedRef.current) return;
    setCopied(false);
    generateDeviceCode();
  };
  
  const handleBackgroundClick = (e: React.MouseEvent) => {
    // 确保只有点击背景时才关闭
    if (e.target === e.currentTarget) {
      console.log('Background clicked, closing modal');
      onClose();
    }
  };
  
  const handleClose = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    console.log('Close button clicked, closing modal');
    onClose();
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleBackgroundClick}
      />
      
      {/* 模态框内容 */}
      <div className="relative w-full max-w-md" onClick={(e) => e.stopPropagation()}>
        
        <div className="device-code-modal card-border overflow-hidden rounded-2xl animate-modal-enter">
          {/* 头部 */}
          <div className="p-6 pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white flex items-center">
                <div className="w-10 h-10 rounded-full glass border border-blue-400/30 flex items-center justify-center mr-3 relative overflow-hidden">
                  {/* 硬件芯片图标 */}
                  <svg className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 7H7v2h2V7zm0 4H7v2h2v-2zm0-8c-.55 0-1 .45-1 1v1H7c-.55 0-1 .45-1 1v1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V8c0-.55-.45-1-1-1h-2V6c0-.55-.45-1-1-1h-1V4c0-.55-.45-1-1-1H9zm6 0h2v1h-2V3zm4 2h2v1h-2V5zM5 9h14v8H5V9z"/>
                    <path d="M11 11h2v2h-2v-2zm4 0h2v2h-2v-2z"/>
                  </svg>
                  {/* 渐变光效 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-orange-400/20 rounded-full"></div>
                </div>
                <div>
                  <span className="block">设备唯一码</span>
                  <span className="text-sm text-white/60 font-normal">Hardware Device ID</span>
                </div>
              </h3>
              <button
                onClick={handleClose}
                className="group relative flex items-center space-x-2 px-3 py-2 rounded-lg bg-red-500/10 border border-red-400/30 hover:bg-red-500/20 hover:border-red-400/50 transition-all duration-200 text-red-300 hover:text-red-200"
                title="关闭设备码窗口"
              >
                {/* 背景渐变效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-red-600/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity"></div>
                
                {/* 关闭图标 */}
                <svg className="w-4 h-4 z-10 group-hover:scale-110 transition-transform" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                
                {/* 文字标签 */}
                <span className="text-xs font-medium z-10">关闭</span>
                
                {/* 键盘提示 */}
                <div className="hidden group-hover:flex items-center space-x-1 text-xs text-red-400/60 z-10">
                  <span>ESC</span>
                </div>
              </button>
            </div>
            <p className="text-white/60 text-sm leading-relaxed">
              基于硬件信息生成的唯一设备标识码，用于软件授权激活
            </p>
          </div>
          
          {/* 设备码显示区域 */}
          <div className="px-6 pb-6">
            <div className="device-code-display gradient-border rounded-xl p-6 mb-4 animate-pulse">
              {isGenerating ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center space-x-3">
                    <div className="device-code-spinner"></div>
                    <span className="text-white/80 text-sm">正在生成设备唯一码...</span>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className="device-code-text font-mono text-xl text-white mb-4 tracking-wider leading-relaxed">
                    {deviceId}
                  </div>
                  <div className="flex items-center justify-center space-x-4 mb-3">
                    <div className="flex items-center glass px-3 py-1.5 rounded-full border border-green-400/20">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                      <span className="text-xs text-green-300 font-medium">已生成</span>
                    </div>
                    <div className="flex items-center glass px-3 py-1.5 rounded-full border border-orange-400/20">
                      <svg className="w-3 h-3 text-orange-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                      </svg>
                      <span className="text-xs text-orange-300 font-medium">硬件绑定</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            {/* 操作按钮 */}
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={handleCopy}
                disabled={isGenerating || !deviceId}
                className="glass flex items-center justify-center px-4 py-3 border border-blue-400/30 rounded-lg hover:bg-blue-400/10 transition text-sm text-white disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500/50 relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                {copied ? (
                  <>
                    <svg className="w-4 h-4 text-green-400 mr-2 z-10" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="z-10">已复制</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 text-blue-400 mr-2 z-10" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                    </svg>
                    <span className="z-10">复制设备码</span>
                  </>
                )}
              </button>
              
              <button
                onClick={handleRefresh}
                disabled={isGenerating}
                className="glass flex items-center justify-center px-4 py-3 border border-orange-400/30 rounded-lg hover:bg-orange-400/10 transition text-sm text-white disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-orange-500/50 relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-orange-400/10 to-orange-600/10 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <svg className={`w-4 h-4 text-orange-400 mr-2 z-10 ${isGenerating ? 'animate-spin' : ''}`} fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                <span className="z-10">重新生成</span>
              </button>
            </div>
            
            {/* 提示信息 */}
            <div className="mt-4 p-4 glass rounded-lg border border-yellow-400/20 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-400/5"></div>
              <div className="flex items-start space-x-3 relative z-10">
                <svg className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div className="text-xs text-yellow-300">
                  <p className="font-medium mb-2 text-sm">设备码使用说明：</p>
                  <div className="space-y-1 text-white/70">
                    <p>• 设备码基于硬件信息生成，每台设备唯一且固定</p>
                    <p>• 请将完整设备码发送给软件提供商获取激活码</p>
                    <p>• 激活码与设备码绑定，仅限当前设备使用</p>
                    <p>• 更换硬件后需要重新获取设备码和激活码</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceCodeModal;