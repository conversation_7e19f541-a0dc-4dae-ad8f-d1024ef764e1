/**
 * 设备码生成工具类
 * 基于硬件信息生成唯一的设备标识码
 * 参考离线授权系统开发方案
 */

interface HardwareInfo {
  cpuId: string;
  motherboardId: string;
  diskId: string;
  macAddress: string;
  installGuid: string;
  browserFingerprint: string;
  osInfo: string;
}

class DeviceIdGenerator {
  private static STORAGE_KEY = 'device_install_guid';
  
  /**
   * 获取CPU信息（浏览器环境下的替代方案）
   */
  private static getCpuInfo(): string {
    try {
      // 在浏览器环境中，我们使用navigator.hardwareConcurrency作为CPU特征
      const cores = navigator.hardwareConcurrency || 4;
      const platform = navigator.platform || 'unknown';
      const memory = (navigator as any).deviceMemory || 'unknown';
      return `${platform}-${cores}cores-${memory}GB`;
    } catch {
      return 'unknown-cpu';
    }
  }
  
  /**
   * 获取操作系统信息
   */
  private static getOSInfo(): string {
    try {
      const userAgent = navigator.userAgent;
      let os = 'unknown';
      
      if (userAgent.includes('Windows')) {
        if (userAgent.includes('Windows NT 10.0')) os = 'Win10';
        else if (userAgent.includes('Windows NT 6.3')) os = 'Win8.1';
        else if (userAgent.includes('Windows NT 6.1')) os = 'Win7';
        else os = 'Windows';
      } else if (userAgent.includes('Macintosh')) {
        os = 'macOS';
      } else if (userAgent.includes('Linux')) {
        os = 'Linux';
      }
      
      return `${os}-${navigator.platform}`;
    } catch {
      return 'unknown-os';
    }
  }
  
  /**
   * 获取屏幕信息作为硬件特征
   */
  private static getScreenInfo(): string {
    try {
      const screen = window.screen;
      return `${screen.width}x${screen.height}-${screen.colorDepth}bit`;
    } catch {
      return 'unknown-screen';
    }
  }
  
  /**
   * 获取时区信息
   */
  private static getTimezoneInfo(): string {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone || 'unknown';
    } catch {
      return 'unknown-timezone';
    }
  }
  
  /**
   * 获取语言信息
   */
  private static getLanguageInfo(): string {
    try {
      return navigator.language || 'unknown';
    } catch {
      return 'unknown-language';
    }
  }
  
  /**
   * 获取浏览器指纹
   */
  private static getBrowserFingerprint(): string {
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        return canvas.toDataURL().slice(-50); // 取最后50个字符作为指纹
      }
      return navigator.userAgent.slice(-50);
    } catch {
      return 'unknown-fingerprint';
    }
  }
  
  /**
   * 获取或生成安装GUID
   */
  private static getInstallGuid(): string {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (stored) {
      return stored;
    }
    
    const guid = this.generateUUID();
    localStorage.setItem(this.STORAGE_KEY, guid);
    return guid;
  }
  
  /**
   * 生成UUID
   */
  private static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * 收集硬件信息
   */
  private static collectHardwareInfo(): HardwareInfo {
    return {
      cpuId: this.getCpuInfo(),
      motherboardId: this.getScreenInfo(), // 使用屏幕信息作为主板替代
      diskId: this.getTimezoneInfo(), // 使用时区信息作为磁盘替代
      macAddress: this.getLanguageInfo(), // 使用语言信息作为MAC替代
      installGuid: this.getInstallGuid(),
      browserFingerprint: this.getBrowserFingerprint(),
      osInfo: this.getOSInfo()
    };
  }
  
  /**
   * 简单的哈希函数
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
  
  /**
   * Base32编码
   */
  private static toBase32(input: string): string {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let result = '';
    let bits = 0;
    let value = 0;
    
    for (let i = 0; i < input.length; i++) {
      value = (value << 8) | input.charCodeAt(i);
      bits += 8;
      
      while (bits >= 5) {
        result += alphabet[(value >>> (bits - 5)) & 31];
        bits -= 5;
      }
    }
    
    if (bits > 0) {
      result += alphabet[(value << (5 - bits)) & 31];
    }
    
    return result;
  }
  
  /**
   * 生成设备码
   */
  public static generateDeviceId(): string {
    try {
      console.log('DeviceIdGenerator: Starting device ID generation...');
      const info = this.collectHardwareInfo();
      console.log('DeviceIdGenerator: Hardware info collected:', info);
      
    // 按字母顺序排序并拼接（参考开发方案）
    const rawString = [
      `CPU:${info.cpuId}`,
      `DISK:${info.diskId}`,
      `FP:${info.browserFingerprint}`,
      `GUID:${info.installGuid}`,
      `MAC:${info.macAddress}`,
      `MB:${info.motherboardId}`,
      `OS:${info.osInfo}`
    ].join('|');      console.log('DeviceIdGenerator: Raw string created:', rawString);
      
      // 生成哈希
      const hash = this.simpleHash(rawString);
      console.log('DeviceIdGenerator: Hash generated:', hash);
      
      // Base32编码并格式化
      const base32 = this.toBase32(hash);
      console.log('DeviceIdGenerator: Base32 encoded:', base32);
      
      const formatted = this.formatDeviceId(base32);
      console.log('DeviceIdGenerator: Final device ID:', formatted);
      
      return formatted;
    } catch (error) {
      console.error('DeviceIdGenerator: Error generating device ID:', error);
      // 返回一个备用的设备码
      const fallbackId = this.generateFallbackId();
      console.log('DeviceIdGenerator: Using fallback ID:', fallbackId);
      return fallbackId;
    }
  }
  
  /**
   * 格式化设备码为5-5-5-5-5格式
   */
  private static formatDeviceId(base32: string): string {
    const cleaned = base32.substring(0, 25).padEnd(25, '0'); // 确保25位长度
    return cleaned.match(/.{1,5}/g)?.join('-') || cleaned;
  }
  
  /**
   * 生成备用设备码
   */
  private static generateFallbackId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 7);
    const fallback = `${timestamp}${random}`.substring(0, 25).padEnd(25, '0');
    return this.formatDeviceId(fallback);
  }
  
  /**
   * 验证设备码格式
   */
  public static validateDeviceId(deviceId: string): boolean {
    const pattern = /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/;
    return pattern.test(deviceId);
  }
  
  /**
   * 获取设备信息摘要（用于调试）
   */
  public static getDeviceInfo(): HardwareInfo {
    return this.collectHardwareInfo();
  }
}

export default DeviceIdGenerator;
export type { HardwareInfo };