# 离线授权系统开发方案（整理版）

版本：2025-09-09  
状态：已完成原始方案（旧方案）与重构优化方案（实施版）并行说明。本文件对原有内容重新编排，提升可读性与维护性。

---
## 目录
1. 概览与定位  
2. 方案分层结构  
3. 核心设计原则  
4. 原始方案（概要）  
5. 重构方案（实施版）  
   5.1 目标与动机  
   5.2 旧 / 新差异矩阵  
   5.3 新数据结构与流程  
   5.4 设备指纹设计  
   5.5 激活码格式（Payload & 签名）  
   5.6 签名器实现要点  
   5.7 客户端验证流程  
   5.8 本地存储与防篡改  
   5.9 多公钥轮换机制  
   5.10 WASM 辅助校验（可选）  
   5.11 安全攻击面与缓解  
   5.12 测试与回归清单  
   5.13 渐进式加固路线图  
   5.14 迁移 / 兼容策略  
   5.15 后续扩展字段  
6. 部署与操作手册（新版）  
7. 风险与边界声明  
8. 结论  
9. 快速 FAQ  
10. 附录 A：旧方案完整代码  
11. 附录 B：重构关键代码片段  
12. 附录 C：混淆 / WASM / 轮换示例  

---
## 1. 概览与定位
离线授权系统用于：在完全无网络环境下，对终端设备进行“一次性、永久”软件激活，绑定单机硬件指纹，防止简单复制与滥用。采用“非对称签名 + 设备指纹哈希 + 本地完整性/HMAC”多层绑定策略。

---
## 2. 方案分层结构
| 层次 | 组件 | 职责 |
|------|------|------|
| 签发侧 | 签名器 CLI | 生成密钥、签发激活码、轮换密钥 |
| 客户端主进程 | 设备指纹采集 | 读取稳定硬件信息 + 安装GUID 固化 |
| 客户端渲染层 | UI / 验证 | 展示设备码，接收激活码，执行签名与指纹校验 |
| 安全支撑 | HMAC / 混淆 / WASM | 防本地伪造与直接篡改逻辑 |

---
## 3. 核心设计原则
- 稳定优先：只采用相对稳定硬件特征，降低售后激活失败比例。
- 明确边界：不追求“绝对防破解”，而是提升批量滥用成本。
- 可演进：提前预留 keyVersion / 结构版本字段，支持后续添加功能 / 时间限制。
- 简洁落地：最小可行安全集→逐步加固（分阶段实施）。

---
## 4. 原始方案（概要）
原始方案要点：
- 设备指纹：CPU / 主板 / 硬盘 / MAC / 安装GUID → 组合后 SHA256 → Base32 前25位 → 5-5-5-5-5 展示。
- 激活码：`base64(payload).base64(signature)`，payload 包含 `{ deviceId, timestamp, version }`。
- 验证：对 payloadBase64 验签 + 比较设备码（去‘-’）。
- 存储：localStorage 保存 deviceId 与 signature（无防篡改）。
- 加固：计划使用代码混淆 / 简单完整性检查。

局限：
1. 浏览器与 Node API 混用（execSync + localStorage）。
2. MAC / 硬盘序列易变，指纹不稳定。 
3. 无设备锁定二次构造（复制激活文件可迁移）。
4. timestamp 未参与任何策略判断。
5. 无密钥轮换落地机制。
6. 无防存档伪造（篡改 JSON 可直接通过）。

完整旧代码迁移至【附录 A】保留参考。

---
## 5. 重构方案（实施版）
### 5.1 目标与动机
- 单设备永久授权 + 提高复制/伪造成本。
- 结构标准化（base64url，无 + / =）。
- 加入设备锁定哈希 dLock + HMAC 防本地篡改。
- 预留 keyVersion 支持多公钥轮换。

### 5.2 旧 / 新差异矩阵
| 项目 | 旧 | 新 |
|------|----|----|
| 指纹字段 | CPU/主板/硬盘/MAC/安装GUID | CPU/主板/系统卷/安装GUID（精简） |
| 指纹展示 | 256bit→截取25 | SHA256→取前130bit→Crockford Base32 5*5 |
| Payload | deviceId,timestamp,version | v,kv,d,sid,ia |
| 激活码编码 | base64 + base64 | base64url + base64url |
| 设备绑定 | 直接对比 deviceId | d = hash(canonical)，再派生 dLock=SHA256(d+sid) |
| 防篡改 | 无 | HMAC(lic+dLock,K_embedded) |
| 轮换 | 文档描述 | kv 实际使用 + 多公钥映射 |
| 加固 | 简单混淆 | 分片/乱序/可选 WASM |

### 5.3 新数据结构与流程
1. 客户端生成 deviceCode (展示) + canonical（内部 25 字符）。  
2. 签名器接收 deviceCode → 计算 deviceHash = SHA256(canonical) → base64url。  
3. 生成 payload：`{ v, kv, d: deviceHash, sid, ia }`。  
4. 激活码：`base64url(payload) + '.' + base64url(RSA-SHA256签名)`。
5. 客户端验证：验签 → 重新计算当前 deviceHash → 匹配 d → 生成 dLock=SHA256(d+sid) → 生成 HMAC → 存储。
6. 后续启动：重算并校验签名 + dLock + HMAC。

### 5.4 设备指纹设计
| 字段 | 说明 | 失败回退 | 变更风险 |
|------|------|----------|----------|
| CPU ProcessorId | wmic 读取 | “NA” | 低 |
| 主板序列 | wmic baseboard | “NA” | 中（个别厂商空） |
| 系统卷序列 | C: 卷序列 | “NA” | 中（重装系统变） |
| 安装GUID | 首次随机 UUID | 永久保存 | 降低其他变更影响 |

组合 JSON → SHA256 → 前 17 字节 → Crockford Base32 → 截 25 字符 → 5-5-5-5-5。

### 5.5 激活码格式
Payload (示例)：
```json
{ "v":1, "kv":1, "d":"<deviceHash>", "sid":"<128-bit随机>", "ia":1736486400000 }
```
- 说明：不包含真实硬件明文，仅含哈希，减少逆推风险。
- 激活码：`<p64u>.<sig64u>`。

### 5.6 签名器实现要点
- 提供命令：`keygen`、`sign <deviceCode>`、`--json` 输出。  
- keyRegistry 维护 activeKeyVersion。  
- buildActivationCode 内部：hash(deviceCode→clean→SHA256→base64url)。

### 5.7 客户端验证流程
1. parse → payload / signature。  
2. 按 kv 选择公钥（未知直接失败）。  
3. verifySig(p64u, sig)。  
4. 取当前 canonical → deviceHashCurrent。  
5. 比较 payload.d === deviceHashCurrent。  
6. 生成 dLock=SHA256(d + sid)。  
7. HMAC( lic + dLock, K_embedded ) → 与存储比对。  
8. 通过即视为激活。  

### 5.8 本地存储与防篡改
| 字段 | 含义 | 校验方法 |
|------|------|----------|
| lic | 原激活码 | 验签 + 解析 |
| dLock | 设备锁定散列 | 重新计算 SHA256(d+sid) 对比 |
| hmac | 防伪签名 | HMAC(lic+dLock,K_embedded) 重算比对 |

### 5.9 多公钥轮换机制
- payload.kv 选择对应公钥。  
- 轮换步骤：添加新公钥 → 客户端同时支持旧+新 → 更新 metadata.json → 新签发使用新 kv → 监控旧 kv 剩余使用量 → 移除。  

### 5.10 WASM 辅助校验（可选）
- 将 HMAC / SHA256 部分迁入 WASM，提升逆向门槛。  
- WASM 导出函数非语义命名 + 填充伪逻辑。  
- JS 层仅做参数准备与结果后处理。

### 5.11 安全攻击面与缓解（摘要）
| 向量 | 威胁 | 缓解 |
|------|------|------|
| 复制激活存档 | 跨机使用 | dLock + 指纹重算 |
| JSON 篡改 | 伪造激活 | HMAC + 混淆 key |
| Patch 校验函数 | 跳过验证 | 多模块 + 冗余 + 混淆 + WASM |
| 替换公钥 | 伪造签名 | 公钥分片 + 完整性检查（规划） |
| 密钥泄露 | 大量伪造 | 快速轮换 kv + 新客户端发布 |
| 虚拟化伪指纹 | 批量生成 | 运营策略识别 / 分发控制 |

### 5.12 测试与回归清单
编号：T1~T10 已列（详见附录 B）确保：激活成功 / 篡改失败 / 跨机失效 / 轮换兼容。

### 5.13 渐进式加固路线图
1. 基础重构（设备码 + 新 payload + HMAC）。  
2. 多公钥 + 分片混淆。  
3. WASM + 冗余路径。  
4. Manifest 完整性（文件哈希白名单）。  
5. 功能授权 / 时限字段。  
6. 离线黑名单差分包（若需要撤销）。

### 5.14 迁移 / 兼容策略
| 场景 | 策略 |
|------|------|
| 旧激活码继续使用 | 添加旧解析分支（限定读取，不重新写入新结构） |
| 过渡期结束 | 移除旧分支 + 发布公告 |
| 客户硬件变化 | 要求重新签发（校验失败时提示） |

### 5.15 后续扩展字段
| 字段 | 说明 | 使用时机 |
|------|------|----------|
| nb / na | 授权时间窗 | 临时/订阅授权 |
| feat[] | 功能集 | 版本差异控制 |
| cid | 客户 ID | 支持/统计/黑名单 |
| sig2 | 第二签名算法 | 高防或司法取证 |

---
## 6. 部署与操作手册（新版）
1. 生成密钥：`activation-signer keygen`（备份 private.pem，嵌入 public.pem）。  
2. 客户端构建：Electron 主进程集成指纹代码 → 渲染层 UI。  
3. 获取设备码：客户复制 5-5-5-5-5 字符串。  
4. 签发：`activation-signer sign XXXXX-XXXXX-XXXXX-XXXXX-XXXXX --json`。  
5. 客户粘贴激活码 → 成功写入 activation_v1。  
6. 后续启动：自动 checkActivated。  
7. 轮换：更新 metadata.json activeKeyVersion → 添加新公钥 → 发布。  

---
## 7. 风险与边界声明
| 范畴 | 说明 |
|------|------|
| 绝对安全 | 不可达；本方案提升“复制 / 批量伪造”成本，仅防机会型滥用 |
| 撤销能力 | 纯离线不支持实时吊销，需要“离线黑名单包”机制（未实现） |
| 大硬件变更 | 视同新设备，需要重新签发 |
| 虚拟化伪装 | 仍可绕过，需结合运营/许可策略控制 |
| 法规与隐私 | 指纹未存储原始硬件明文；可在用户协议中披露采集类别 |

---
## 8. 结论
重构方案在不显著增加实现复杂度的情况下，补齐旧方案的：
- 指纹稳定性
- 设备锁定与存档防篡改
- 密钥轮换可行性
- 混淆与加固扩展点
为后续“时间授权 / 功能授权 / 黑名单撤销”留下接口。

---
## 9. 快速 FAQ
| 问题 | 回答 |
|------|------|
| 客户换主板后激活失效？ | 属于设备指纹核心变更，需要重新签发 |
| 激活码能否离线批量生成？ | 是，签名器支持脚本化；建议加入工单流程追踪 |
| 激活文件被复制能用吗？ | 不能（dLock + HMAC 与本机指纹绑定） |
| 可以限制有效期吗？ | 可添加 nb/na 字段（当前未启用） |
| 支持多少密钥版本并存？ | 视客户端内置公钥映射，建议不超过 3 个并逐步淘汰 |

---
## 10. 附录 A：旧方案完整代码
（原始内容保留，未改动）

```markdown
# 离线授权系统开发方案

## 1. 系统架构概述

### 1.1 核心组件

```mermaid
graph TB
    A[客户端软件] --> B[设备码生成器]
    A --> C[激活码验证器]
    D[签名器工具] --> E[私钥管理]
    F[客户] --> G[设备码]
    G --> D
    D --> H[激活码]
    H --> A
```

### 1.2 技术栈选择

| 组件 | 技术栈 | 理由 |
|------|--------|------|
| 客户端软件 | React + TypeScript + Electron | 跨平台桌面应用，现有技术栈 |
| 设备码生成 | Node.js + crypto | 与主应用技术栈一致 |
| 签名器工具 | Node.js + Commander.js | 命令行工具，便于自动化 |
| 加密算法 | RSA-2048 / ECC-P256 | 行业标准，安全可靠 |

## 2. 设备码生成模块

### 2.1 硬件信息采集

```typescript
// src/utils/deviceId.ts
import { createHash } from 'crypto';
import { execSync } from 'child_process';
import { v4 as uuidv4 } from 'uuid';

interface HardwareInfo {
  cpuId: string;
  motherboardId: string;
  diskId: string;
  macAddress: string;
  installGuid: string;
}

class DeviceIdGenerator {
  private static STORAGE_KEY = 'device_install_guid';
  
  /**
   * 获取CPU序列号
   */
  private static getCpuId(): string {
    try {
      const result = execSync('wmic cpu get ProcessorId /value', { encoding: 'utf8' });
      const match = result.match(/ProcessorId=(.+)/);
      return match ? match[1].trim() : 'unknown';
    } catch {
      return 'unknown';
    }
  }
  
  /**
   * 获取主板序列号
   */
  private static getMotherboardId(): string {
    try {
      const result = execSync('wmic baseboard get SerialNumber /value', { encoding: 'utf8' });
      const match = result.match(/SerialNumber=(.+)/);
      return match ? match[1].trim() : 'unknown';
    } catch {
      return 'unknown';
    }
  }
  
  /**
   * 获取硬盘序列号
   */
  private static getDiskId(): string {
    try {
      const result = execSync('wmic diskdrive get SerialNumber /value', { encoding: 'utf8' });
      const match = result.match(/SerialNumber=(.+)/);
      return match ? match[1].trim() : 'unknown';
    } catch {
      return 'unknown';
    }
  }
  
  /**
   * 获取MAC地址
   */
  private static getMacAddress(): string {
    try {
      const result = execSync('getmac /fo csv /nh', { encoding: 'utf8' });
      const match = result.match(/"([^"]+)"/); 
      return match ? match[1].replace(/-/g, '') : 'unknown';
    } catch {
      return 'unknown';
    }
  }
  
  /**
   * 获取或生成安装GUID
   */
  private static getInstallGuid(): string {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (stored) {
      return stored;
    }
    
    const guid = uuidv4();
    localStorage.setItem(this.STORAGE_KEY, guid);
    return guid;
  }
  
  /**
   * 收集硬件信息
   */
  private static collectHardwareInfo(): HardwareInfo {
    return {
      cpuId: this.getCpuId(),
      motherboardId: this.getMotherboardId(),
      diskId: this.getDiskId(),
      macAddress: this.getMacAddress(),
      installGuid: this.getInstallGuid()
    };
  }
  
  /**
   * 生成设备码
   */
  public static generateDeviceId(): string {
    const info = this.collectHardwareInfo();
    
    // 按字母顺序排序并拼接
    const rawString = [
      `CPU:${info.cpuId}`,
      `DISK:${info.diskId}`,
      `GUID:${info.installGuid}`,
      `MAC:${info.macAddress}`,
      `MB:${info.motherboardId}`
    ].join('|');
    
    // SHA-256哈希
    const hash = createHash('sha256').update(rawString).digest();
    
    // Base32编码并格式化
    const base32 = this.toBase32(hash);
    return this.formatDeviceId(base32);
  }
  
  /**
   * Base32编码
   */
  private static toBase32(buffer: Buffer): string {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let result = '';
    let bits = 0;
    let value = 0;
    
    for (const byte of buffer) {
      value = (value << 8) | byte;
      bits += 8;
      
      while (bits >= 5) {
        result += alphabet[(value >>> (bits - 5)) & 31];
        bits -= 5;
      }
    }
    
    if (bits > 0) {
      result += alphabet[(value << (5 - bits)) & 31];
    }
    
    return result;
  }
  
  /**
   * 格式化设备码为5-5-5-5-5格式
   */
  private static formatDeviceId(base32: string): string {
    const cleaned = base32.substring(0, 25); // 取前25位
    return cleaned.match(/.{1,5}/g)?.join('-') || cleaned;
  }
}

export default DeviceIdGenerator;
```

### 2.2 设备码组件

```typescript
// src/components/DeviceIdDisplay.tsx
import React, { useState, useEffect } from 'react';
import DeviceIdGenerator from '../utils/deviceId';

const DeviceIdDisplay: React.FC = () => {
  const [deviceId, setDeviceId] = useState<string>('');
  const [copied, setCopied] = useState(false);
  
  useEffect(() => {
    const id = DeviceIdGenerator.generateDeviceId();
    setDeviceId(id);
  }, []);
  
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(deviceId);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };
  
  return (
    <div className="device-id-container">
      <h3>设备码</h3>
      <div className="device-id-display">
        <code>{deviceId}</code>
        <button onClick={handleCopy} className="copy-btn">
          {copied ? '已复制' : '复制'}
        </button>
      </div>
      <p className="help-text">
        请将此设备码发送给软件提供商以获取激活码
      </p>
    </div>
  );
};

export default DeviceIdDisplay;
```

## 3. 签名器工具开发

### 3.1 密钥管理

```typescript
// tools/signer/src/keyManager.ts
import { generateKeyPairSync, createSign, createVerify } from 'crypto';
import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';

interface KeyPair {
  publicKey: string;
  privateKey: string;
}

class KeyManager {
  private static readonly KEY_DIR = './keys';
  private static readonly PRIVATE_KEY_FILE = 'private.pem';
  private static readonly PUBLIC_KEY_FILE = 'public.pem';
  
  /**
   * 生成RSA密钥对
   */
  public static generateKeyPair(): KeyPair {
    const { publicKey, privateKey } = generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });
    
    return { publicKey, privateKey };
  }
  
  /**
   * 保存密钥对到文件
   */
  public static saveKeyPair(keyPair: KeyPair): void {
    if (!existsSync(this.KEY_DIR)) {
      require('fs').mkdirSync(this.KEY_DIR, { recursive: true });
    }
    
    writeFileSync(join(this.KEY_DIR, this.PRIVATE_KEY_FILE), keyPair.privateKey);
    writeFileSync(join(this.KEY_DIR, this.PUBLIC_KEY_FILE), keyPair.publicKey);
    
    console.log('密钥对已保存到:', this.KEY_DIR);
  }
  
  /**
   * 加载私钥
   */
  public static loadPrivateKey(): string {
    const keyPath = join(this.KEY_DIR, this.PRIVATE_KEY_FILE);
    if (!existsSync(keyPath)) {
      throw new Error('私钥文件不存在，请先生成密钥对');
    }
    return readFileSync(keyPath, 'utf8');
  }
  
  /**
   * 加载公钥
   */
  public static loadPublicKey(): string {
    const keyPath = join(this.KEY_DIR, this.PUBLIC_KEY_FILE);
    if (!existsSync(keyPath)) {
      throw new Error('公钥文件不存在，请先生成密钥对');
    }
    return readFileSync(keyPath, 'utf8');
  }
  
  /**
   * 签名数据
   */
  public static signData(data: string, privateKey: string): string {
    const sign = createSign('RSA-SHA256');
    sign.update(data);
    return sign.sign(privateKey, 'base64');
  }
  
  /**
   * 验证签名
   */
  public static verifySignature(data: string, signature: string, publicKey: string): boolean {
    const verify = createVerify('RSA-SHA256');
    verify.update(data);
    return verify.verify(publicKey, signature, 'base64');
  }
}

export default KeyManager;
```

### 3.2 激活码生成器

```typescript
// tools/signer/src/activationGenerator.ts
import KeyManager from './keyManager';

interface ActivationPayload {
  deviceId: string;
  timestamp: number;
  version: string;
}

class ActivationGenerator {
  /**
   * 生成激活码
   */
  public static generateActivationCode(deviceId: string): string {
    const payload: ActivationPayload = {
      deviceId: deviceId.replace(/-/g, ''), // 移除分隔符
      timestamp: Date.now(),
      version: '1.0'
    };
    
    const payloadJson = JSON.stringify(payload);
    const payloadBase64 = Buffer.from(payloadJson).toString('base64');
    
    // 使用私钥签名
    const privateKey = KeyManager.loadPrivateKey();
    const signature = KeyManager.signData(payloadBase64, privateKey);
    
    // 组合激活码: payload.signature
    const activationCode = `${payloadBase64}.${signature}`;
    
    // 格式化为便于输入的格式
    return this.formatActivationCode(activationCode);
  }
  
  /**
   * 验证激活码格式
   */
  public static validateActivationCode(activationCode: string, deviceId: string): boolean {
    try {
      const cleanCode = activationCode.replace(/-/g, '');
      const [payloadBase64, signature] = cleanCode.split('.');
      
      if (!payloadBase64 || !signature) {
        return false;
      }
      
      // 验证签名
      const publicKey = KeyManager.loadPublicKey();
      if (!KeyManager.verifySignature(payloadBase64, signature, publicKey)) {
        return false;
      }
      
      // 验证设备码
      const payloadJson = Buffer.from(payloadBase64, 'base64').toString();
      const payload: ActivationPayload = JSON.parse(payloadJson);
      
      return payload.deviceId === deviceId.replace(/-/g, '');
    } catch {
      return false;
    }
  }
  
  /**
   * 格式化激活码为便于输入的格式
   */
  private static formatActivationCode(code: string): string {
    // 每8个字符插入一个分隔符
    return code.match(/.{1,8}/g)?.join('-') || code;
  }
}

export default ActivationGenerator;
```

### 3.3 命令行工具

```typescript
// tools/signer/src/cli.ts
import { Command } from 'commander';
import KeyManager from './keyManager';
import ActivationGenerator from './activationGenerator';

const program = new Command();

program
  .name('activation-signer')
  .description('离线激活码签名工具')
  .version('1.0.0');

// 生成密钥对命令
program
  .command('keygen')
  .description('生成RSA密钥对')
  .action(() => {
    console.log('正在生成RSA-2048密钥对...');
    const keyPair = KeyManager.generateKeyPair();
    KeyManager.saveKeyPair(keyPair);
    console.log('密钥对生成完成！');
    console.log('\n⚠️  重要提醒:');
    console.log('1. 请妥善保管私钥文件，不要泄露');
    console.log('2. 公钥需要嵌入到客户端软件中');
    console.log('3. 建议将私钥备份到离线存储设备');
  });

// 生成激活码命令
program
  .command('sign <deviceId>')
  .description('为指定设备码生成激活码')
  .action((deviceId: string) => {
    try {
      console.log(`正在为设备码 ${deviceId} 生成激活码...`);
      const activationCode = ActivationGenerator.generateActivationCode(deviceId);
      console.log('\n激活码生成成功:');
      console.log('=' .repeat(50));
      console.log(activationCode);
      console.log('=' .repeat(50));
      console.log('\n请将此激活码发送给客户');
    } catch (error) {
      console.error('生成激活码失败:', error.message);
      process.exit(1);
    }
  });

// 验证激活码命令
program
  .command('verify <activationCode> <deviceId>')
  .description('验证激活码是否有效')
  .action((activationCode: string, deviceId: string) => {
    try {
      const isValid = ActivationGenerator.validateActivationCode(activationCode, deviceId);
      if (isValid) {
        console.log('✅ 激活码验证成功');
      } else {
        console.log('❌ 激活码验证失败');
      }
    } catch (error) {
      console.error('验证失败:', error.message);
      process.exit(1);
    }
  });

program.parse();
```

## 4. 客户端激活验证

### 4.1 激活码验证组件

```typescript
// src/components/ActivationForm.tsx
import React, { useState } from 'react';
import ActivationValidator from '../utils/activationValidator';
import DeviceIdGenerator from '../utils/deviceId';

interface ActivationFormProps {
  onActivationSuccess: () => void;
}

const ActivationForm: React.FC<ActivationFormProps> = ({ onActivationSuccess }) => {
  const [activationCode, setActivationCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState('');
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsValidating(true);
    setError('');
    
    try {
      const deviceId = DeviceIdGenerator.generateDeviceId();
      const isValid = await ActivationValidator.validateAndSave(activationCode, deviceId);
      
      if (isValid) {
        onActivationSuccess();
      } else {
        setError('激活码无效或与当前设备不匹配');
      }
    } catch (err) {
      setError('激活验证失败，请检查激活码格式');
    } finally {
      setIsValidating(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="activation-form">
      <h3>软件激活</h3>
      
      <div className="form-group">
        <label htmlFor="activationCode">激活码:</label>
        <textarea
          id="activationCode"
          value={activationCode}
          onChange={(e) => setActivationCode(e.target.value)}
          placeholder="请输入激活码"
          rows={4}
          required
        />
      </div>
      
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      <button 
        type="submit" 
        disabled={isValidating || !activationCode.trim()}
        className="activate-btn"
      >
        {isValidating ? '验证中...' : '激活软件'}
      </button>
    </form>
  );
};

export default ActivationForm;
```

### 4.2 激活验证器

```typescript
// src/utils/activationValidator.ts
import { createVerify } from 'crypto';

// 嵌入的公钥（实际使用时应该混淆）
const PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`;

interface ActivationPayload {
  deviceId: string;
  timestamp: number;
  version: string;
}

class ActivationValidator {
  private static readonly STORAGE_KEY = 'activation_info';
  
  /**
   * 验证并保存激活信息
   */
  public static async validateAndSave(activationCode: string, deviceId: string): Promise<boolean> {
    try {
      // 清理激活码格式
      const cleanCode = activationCode.replace(/[-\s]/g, '');
      const [payloadBase64, signature] = cleanCode.split('.');
      
      if (!payloadBase64 || !signature) {
        return false;
      }
      
      // 验证签名
      if (!this.verifySignature(payloadBase64, signature)) {
        return false;
      }
      
      // 解析payload
      const payloadJson = Buffer.from(payloadBase64, 'base64').toString();
      const payload: ActivationPayload = JSON.parse(payloadJson);
      
      // 验证设备码匹配
      const cleanDeviceId = deviceId.replace(/-/g, '');
      if (payload.deviceId !== cleanDeviceId) {
        return false;
      }
      
      // 保存激活信息
      const activationInfo = {
        deviceId: cleanDeviceId,
        activatedAt: Date.now(),
        version: payload.version,
        signature: signature
      };
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(activationInfo));
      return true;
      
    } catch (error) {
      console.error('激活验证失败:', error);
      return false;
    }
  }
  
  /**
   * 检查激活状态
   */
  public static checkActivationStatus(): boolean {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) {
        return false;
      }
      
      const activationInfo = JSON.parse(stored);
      const currentDeviceId = DeviceIdGenerator.generateDeviceId().replace(/-/g, '');
      
      // 验证设备码是否匹配（防止激活文件被复制）
      return activationInfo.deviceId === currentDeviceId;
      
    } catch {
      return false;
    }
  }
  
  /**
   * 验证数字签名
   */
  private static verifySignature(data: string, signature: string): boolean {
    try {
      const verify = createVerify('RSA-SHA256');
      verify.update(data);
      return verify.verify(PUBLIC_KEY, signature, 'base64');
    } catch {
      return false;
    }
  }
  
  /**
   * 清除激活信息
   */
  public static clearActivation(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }
}

export default ActivationValidator;
```

## 5. 安全加固措施

### 5.1 代码混淆配置

```javascript
// webpack.config.js
const JavaScriptObfuscator = require('webpack-obfuscator');

module.exports = {
  // ... 其他配置
  plugins: [
    new JavaScriptObfuscator({
      rotateStringArray: true,
      stringArray: true,
      stringArrayThreshold: 0.8,
      transformObjectKeys: true,
      unicodeEscapeSequence: false
    }, ['excluded_bundle_name.js'])
  ]
};
```

### 5.2 完整性检查

```typescript
// src/utils/integrityChecker.ts
import { createHash } from 'crypto';

class IntegrityChecker {
  // 预计算的应用程序哈希值
  private static readonly EXPECTED_HASH = 'sha256-hash-of-main-bundle';
  
  /**
   * 检查应用程序完整性
   */
  public static checkIntegrity(): boolean {
    try {
      // 在Electron环境中检查主进程文件
      const { app } = require('electron');
      const mainPath = app.getAppPath();
      
      // 计算关键文件的哈希值
      const fs = require('fs');
      const path = require('path');
      const mainBundle = fs.readFileSync(path.join(mainPath, 'main.js'));
      const currentHash = createHash('sha256').update(mainBundle).digest('hex');
      
      return currentHash === this.EXPECTED_HASH;
    } catch {
      return false;
    }
  }
  
  /**
   * 定期完整性检查
   */
  public static startPeriodicCheck(): void {
    setInterval(() => {
      if (!this.checkIntegrity()) {
        console.warn('应用程序完整性检查失败');
        // 可以选择退出应用或显示警告
      }
    }, 60000); // 每分钟检查一次
  }
}

export default IntegrityChecker;
```

## 6. 部署与使用流程

### 6.1 开发环境设置

```bash
# 1. 安装依赖
npm install

# 2. 构建签名器工具
cd tools/signer
npm install
npm run build

# 3. 生成密钥对
node dist/cli.js keygen

# 4. 将公钥嵌入客户端代码
# 复制 keys/public.pem 内容到 src/utils/activationValidator.ts
```

### 6.2 生产环境部署

```bash
# 1. 构建客户端应用
npm run build
npm run electron:build

# 2. 签名器使用
# 客户提供设备码: ABCDE-FGHIJ-KLMNO-PQRST-UVWXY
node dist/cli.js sign ABCDE-FGHIJ-KLMNO-PQRST-UVWXY

# 3. 将生成的激活码发送给客户
```

### 6.3 客户激活流程

1. 客户运行软件，获取设备码
2. 将设备码发送给软件提供商
3. 软件提供商使用签名器生成激活码
4. 客户输入激活码完成激活
5. 软件永久激活，仅限当前设备

## 7. 监控与维护

### 7.1 激活日志记录

```typescript
// src/utils/activationLogger.ts
class ActivationLogger {
  private static readonly LOG_KEY = 'activation_logs';
  
  public static logActivationAttempt(success: boolean, deviceId: string, timestamp: number): void {
    const logs = this.getLogs();
    logs.push({
      success,
      deviceId: deviceId.substring(0, 10) + '***', // 部分隐藏
      timestamp,
      userAgent: navigator.userAgent
    });
    
    // 只保留最近100条记录
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }
    
    localStorage.setItem(this.LOG_KEY, JSON.stringify(logs));
  }
  
  private static getLogs(): any[] {
    try {
      const stored = localStorage.getItem(this.LOG_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }
}

export default ActivationLogger;
```

### 7.2 密钥轮换策略

```markdown
## 密钥轮换计划

### 轮换周期
- 正常情况：每2年轮换一次
- 安全事件：立即轮换
- 人员变动：考虑轮换

### 轮换步骤
1. 生成新密钥对
2. 更新客户端支持多公钥验证
3. 发布新版本客户端
4. 使用新私钥签名新激活码
5. 逐步淘汰旧密钥

### 向后兼容
- 客户端同时支持新旧公钥
- 激活码包含密钥版本标识
- 旧激活码继续有效
```

## 8. 总结

本方案提供了一套完整的离线授权系统，具备以下特点：

✅ **安全可靠**：基于RSA-2048非对称加密，私钥离线保存  
✅ **设备绑定**：激活码与硬件信息强绑定，无法跨设备使用  
✅ **永久激活**：一次激活永久有效，无需联网验证  
✅ **易于部署**：签名器为命令行工具，操作简单  
✅ **可扩展性**：支持密钥轮换和版本升级  
✅ **防篡改**：代码混淆和完整性检查  

> 上述 1~8 章节为初始设计（旧方案基线），下面给出针对“单设备、一次性、永久离线激活 + 提高破解成本”的优化重构实施版。旧内容可保留用于对比与回滚。

---

## 9. 优化重构方案（实施版）

### 9.1 设计目标
- 仅激活一台设备，永久有效，不设计过期/授权等级（后续可扩展）。
- 降低售后风险：减少不稳定硬件字段依赖（如 MAC / 可变磁盘序列）。
- 提升破解门槛：签名 + 设备锁定哈希 + HMAC 防本地伪造 + 公钥/密钥分片混淆基础位。
- 预留密钥轮换（keyVersion）。

### 9.2 与旧方案差异概览
| 项目 | 旧方案 | 重构方案 |
|------|--------|----------|
| 设备指纹来源 | CPU / 主板 / 硬盘 / MAC / 安装GUID | CPU / 主板 / 系统卷(或卷序) / 安装GUID（精简） |
| 指纹展示 | 256bit→Base32取前25字符 | SHA256(JSON)→取前130bit→Crockford Base32 5-5-5-5-5 |
| Payload 字段 | deviceId,timestamp,version | v,kv,d,sid,ia (精简+预留) |
| 签名数据 | base64(payload) | base64url(payload) （无 + / =） |
| 设备锁定 | 直接比对 deviceId | d(设备哈希) + sid → dLock=SHA256(d+sid) 存储校验 |
| 防篡改 | 无 | HMAC(lic + dLock, K_embedded) |
| 混淆 | 单纯公钥 | 公钥+HMAC Key 分片（示例） |
| 密钥轮换 | 文档描述 | kv 字段即插即用 |

### 9.3 设备码生成（Electron 主进程）
- 在主进程调用底层命令（Windows 示例）。其他平台可扩展 provider。 
- 生成 JSON → SHA256 → 取前 17 字节(≈136 bit) → Base32(Crockford) → 截取 25 字符 → 展示为 5 组。
- 不直接暴露原始硬件序列，保护隐私与实现细节。

```typescript
// src/main/device-fingerprint.ts
import { createHash, randomUUID, randomBytes } from 'crypto';
import { execSync } from 'child_process';
import { app } from 'electron';
import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

function safe(cmd: string, pattern: RegExp, fallback = 'NA'): string {
  try {
    const out = execSync(cmd, { encoding: 'utf8', stdio: ['ignore','pipe','ignore'] });
    const m = out.match(pattern);
    return m ? (m[1].trim() || fallback) : fallback;
  } catch { return fallback; }
}

function getCpu(): string { return safe('wmic cpu get ProcessorId /value', /ProcessorId=(.+)/i); }
function getBoard(): string { return safe('wmic baseboard get SerialNumber /value', /SerialNumber=(.+)/i); }
function getSysVol(): string { return safe('wmic volume where "DriveLetter=\'C:\'" get SerialNumber /value', /SerialNumber=(.+)/i); }

function loadOrCreateInstallGuid(): string {
  const dir = join(app.getPath('userData'), 'lic');
  const file = join(dir, 'install-guid');
  if (existsSync(file)) return readFileSync(file, 'utf8').trim();
  if (!existsSync(dir)) mkdirSync(dir, { recursive: true });
  const g = randomUUID();
  writeFileSync(file, g, 'utf8');
  return g;
}

const ALPH = '0123456789ABCDEFGHJKMNPQRSTVWXYZ'; // Crockford 去掉 I L O U
function toBase32Crockford(buf: Buffer): string {
  let bits = 0, value = 0, out = '';
  for (const b of buf) {
    value = (value << 8) | b; bits += 8;
    while (bits >= 5) { out += ALPH[(value >>> (bits - 5)) & 31]; bits -= 5; }
  }
  if (bits > 0) out += ALPH[(value << (5 - bits)) & 31];
  return out;
}

export function computeDeviceCode() {
  const payload = { c: getCpu(), b: getBoard(), v: getSysVol(), g: loadOrCreateInstallGuid() };
  const hash = createHash('sha256').update(JSON.stringify(payload)).digest();
  const first17 = hash.subarray(0, 17); // 136bit
  const b32 = toBase32Crockford(first17); // ~27 字符
  const canonical = b32.substring(0, 25); // 截取 25 展示
  const deviceCode = canonical.match(/.{1,5}/g)!.join('-');
  return { deviceCode, canonical }; // canonical 供后续派生哈希
}
```

IPC 暴露：
```typescript
// src/main/ipc.ts
import { ipcMain } from 'electron';
import { computeDeviceCode } from './device-fingerprint';
ipcMain.handle('license:getDeviceCode', () => computeDeviceCode());
```

渲染进程获取：
```typescript
// src/renderer/utils/deviceId.ts
export async function getDeviceCode(): Promise<{deviceCode:string; canonical:string}> {
  return window.electron.invoke('license:getDeviceCode');
}
```

### 9.4 激活码结构
Payload (JSON → base64url)：
```json
{
  "v":1,     // license schema version
  "kv":1,    // key version (密钥轮换)
  "d":"<deviceHash>", // 对 canonical 再 SHA256 → base64url
  "sid":"<salt128b>", // 128bit 随机盐，防枚举 & 设备锁定
  "ia":1736486400000    // issuedAt(ms)
}
```
激活码：`base64url(payload)` + `.` + `base64url(signature)`
签名：RSA-SHA256( payloadBase64Url )。

### 9.5 签名器（Node CLI）

```typescript
// tools/signer/src/base64url.ts
export function toB64u(buf: Buffer): string {
  return buf.toString('base64').replace(/\+/g,'-').replace(/\//g,'_').replace(/=+$/,'');
}
export function fromB64u(s: string): Buffer {
  const pad = s.length % 4 === 0 ? '' : '='.repeat(4 - (s.length % 4));
  return Buffer.from(s.replace(/-/g,'+').replace(/_/g,'/') + pad, 'base64');
}
```
```typescript
// tools/signer/src/payload.ts
import { toB64u, fromB64u } from './base64url';
export interface LicensePayload { v:number; kv:number; d:string; sid:string; ia:number; }
export const encodePayload = (p:LicensePayload) => toB64u(Buffer.from(JSON.stringify(p)));
export const decodePayload = (b64u:string):LicensePayload => JSON.parse(fromB64u(b64u).toString('utf8'));
```
```typescript
// tools/signer/src/signer.ts
import { createSign, createHash, randomBytes } from 'crypto';
import KeyManager from './keyManager';
import { encodePayload, LicensePayload } from './payload';
import { toB64u } from './base64url';

function deviceHashFromCode(deviceCode: string): string {
  const clean = deviceCode.replace(/-/g,'').toUpperCase();
  return toB64u(createHash('sha256').update(clean).digest());
}

export function buildActivationCode(deviceCode: string) {
  const payload: LicensePayload = {
    v:1,
    kv:1,
    d: deviceHashFromCode(deviceCode),
    sid: toB64u(randomBytes(16)),
    ia: Date.now()
  };
  const p64 = encodePayload(payload);
  const priv = KeyManager.loadPrivateKey();
  const sign = createSign('RSA-SHA256');
  sign.update(p64);
  const sig = toB64u(Buffer.from(sign.sign(priv,'base64'),'base64'));
  return { activationCode: `${p64}.${sig}`, payload };
}
```
```typescript
// tools/signer/src/cli.ts
#!/usr/bin/env node
import { Command } from 'commander';
import KeyManager from './keyManager';
import { buildActivationCode } from './signer';

const program = new Command();
program.name('activation-signer').version('1.1.0');

program.command('keygen').description('生成密钥对').action(()=>{
  const kp = KeyManager.generateKeyPair();
  KeyManager.saveKeyPair(kp);
  console.log('密钥已生成：keys/private.pem & keys/public.pem');
});

program.command('sign <deviceCode>').option('--json','JSON输出').action((deviceCode,opts)=>{
  try {
    const { activationCode, payload } = buildActivationCode(deviceCode);
    if (opts.json) {
      console.log(JSON.stringify({ deviceCode, activationCode, keyVersion: payload.kv, issuedAt: payload.ia }, null, 2));
    } else {
      console.log('激活码:');
      console.log(segment(activationCode,8));
    }
  } catch(e:any){ console.error('生成失败:', e.message); process.exit(1);} });

function segment(s:string,n:number){return s.match(new RegExp(`.{1,${n}}`,'g'))?.join('-')||s;}
program.parse();
```

### 9.6 客户端验证

```typescript
// src/renderer/utils/base64url.ts
export const b64u = {
  enc:(b:Buffer)=>b.toString('base64').replace(/\+/g,'-').replace(/\//g,'_').replace(/=+$/,''),
  dec:(s:string)=>{const pad=s.length%4===0?'':'='.repeat(4-(s.length%4));return Buffer.from(s.replace(/-/g,'+').replace(/_/g,'/')+pad,'base64');}
};
```
```typescript
// src/renderer/utils/licenseValidator.ts
import { createVerify, createHmac, createHash } from 'crypto';
import { b64u } from './base64url';
import { getDeviceCode } from './deviceId';

// 公钥 + HMAC Key 分片（示意，可进一步混淆）
const P1 = '-----BEGIN PUBLIC KEY-----\n';
const P2 = '...实际公钥内容...';
const P3 = '\n-----END PUBLIC KEY-----';
const PUBLIC_KEY = P1 + P2 + P3;
const K_PARTS = ['A1','b9','Z_','7q','x!','m#','R2','p@'];
function buildHmacKey(){ return Buffer.from(K_PARTS.slice().reverse().join(''), 'utf8'); }

interface Payload { v:number; kv:number; d:string; sid:string; ia:number; }

function parse(code:string){
  const clean = code.replace(/\s|-/g,'');
  const sp = clean.split('.');
  if (sp.length!==2) return null;
  try { const pj = b64u.dec(sp[0]).toString('utf8'); return { payload: JSON.parse(pj) as Payload, p64: sp[0], s64: sp[1] }; } catch { return null; }
}

function verifySig(p64:string, s64:string){
  try {
    const v = createVerify('RSA-SHA256');
    v.update(p64);
    const sig = Buffer.from(s64.replace(/-/g,'+').replace(/_/g,'/'), 'base64');
    return v.verify(PUBLIC_KEY, sig);
  } catch { return false; }
}

function deviceHash(canonical:string){
  return b64u.enc(createHash('sha256').update(canonical).digest());
}

function sha256Hex(s:string){ return createHash('sha256').update(s).digest('hex'); }
function hmacHex(data:string){ return createHmac('sha256', buildHmacKey()).update(data).digest('hex'); }

const STORE_KEY = 'activation_v1';

export async function activate(code:string){
  const parsed = parse(code); if(!parsed) return false;
  if(!verifySig(parsed.p64, parsed.s64)) return false;
  const { canonical } = await getDeviceCode();
  const currentHash = deviceHash(canonical);
  if (parsed.payload.d !== currentHash) return false;
  const dLock = sha256Hex(currentHash + parsed.payload.sid);
  const licRaw = parsed.p64 + '.' + parsed.s64;
  const hmac = hmacHex(licRaw + dLock);
  localStorage.setItem(STORE_KEY, JSON.stringify({ lic: licRaw, dLock, hmac }));
  return true;
}

export async function checkActivated(){
  const raw = localStorage.getItem(STORE_KEY); if(!raw) return false;
  try {
    const stored = JSON.parse(raw);
    const parsed = parse(stored.lic); if(!parsed) return false;
    if(!verifySig(parsed.p64, parsed.s64)) return false;
    const { canonical } = await getDeviceCode();
    const currentHash = deviceHash(canonical);
    const dLock = sha256Hex(currentHash + parsed.payload.sid);
    if (dLock !== stored.dLock) return false;
    return hmacHex(stored.lic + stored.dLock) === stored.hmac;
  } catch { return false; }
}

export function revoke(){ localStorage.removeItem(STORE_KEY); }
```

组件示例：
```typescript
// src/renderer/components/DeviceCodeDisplay.tsx
import React,{useEffect,useState} from 'react';
import { getDeviceCode } from '../utils/deviceId';
export const DeviceCodeDisplay:React.FC=()=>{ const [code,setCode]=useState(''); useEffect(()=>{getDeviceCode().then(r=>setCode(r.deviceCode));},[]); return <pre>{code}</pre>; };
```
```typescript
// src/renderer/components/ActivationForm.tsx
import React,{useState} from 'react';
import { activate } from '../utils/licenseValidator';
export const ActivationForm:React.FC<{onSuccess:()=>void}> = ({onSuccess}) => {
  const [val,setVal]=useState('');
  const [loading,setLoading]=useState(false);
  const [err,setErr]=useState('');
  async function submit(e:React.FormEvent){ e.preventDefault(); setLoading(true); setErr(''); const ok = await activate(val.trim()); setLoading(false); if(ok) onSuccess(); else setErr('激活失败'); }
  return (<form onSubmit={submit}><textarea value={val} onChange={e=>setVal(e.target.value)} rows={4} placeholder="粘贴激活码"/><button disabled={!val||loading}>{loading?'验证中...':'激活'}</button>{err&&<div style={{color:'red'}}>{err}</div>}</form>);
};
```

### 9.7 存储与防篡改机制
| 元素 | 作用 |
|------|------|
| lic (原始激活码) | 保留原文，便于支持多公钥/重验 |
| dLock = SHA256(d + sid) | 绑定当前设备指纹与盐，快速一致性校验 |
| hmac = HMAC(lic + dLock) | 防止本地 JSON 被伪造或改写 |

攻击者需要同时：伪造 lic 可通过公钥验签、伪造 dLock 匹配当前设备、伪造 HMAC（需破坏内置混淆 key）。多步提升成本。

### 9.8 混淆与加固建议（可分阶段）
1. 公钥 / HMAC Key 分片 + 乱序 + 运行时拼接。  
2. 将 verifySig 抽离到 WASM 模块（阻断直接文本搜索）。  
3. licenseValidator 拆为多个动态 import 分支 + 伪冗余逻辑。  
4. 启动时随机延迟/多路径校验，统一失败处理。  
5. 构建后进行 selective obfuscation，仅对包含密钥常量的模块混淆。  

### 9.9 实施步骤（优先级）
1. 引入新签名器并生成新激活码（测试环境）。  
2. 客户端加入新验证逻辑与 UI（保持旧逻辑一段时间兼容）。  
3. 发布新版本后，后续全部发放新格式激活码。  
4. 观察问题反馈后移除旧路径。  
5. 加入混淆与（可选）WASM 校验。  

### 9.10 迁移兼容（可选）
- 若已发放旧激活码：在解析失败时回退到旧解析（仅验证一次，不存新结构），提示用户申请新激活码。  
- 一段过渡期后删除旧方案代码。  

### 9.11 边界与声明
- 纯离线无法撤销/黑名单；需撤销请引入“离线黑名单包”+ 定期手动更新。  
- 大幅硬件更换（主板/CPU/系统盘）视为新设备。  
- 虚拟化环境可伪造指纹，本方案目标是“提高滥用成本”，非绝对防御。  

### 9.12 后续可扩展字段
| 字段 | 说明 | 何时启用 |
|------|------|---------|
| nb / na | notBefore / notAfter 限时许可 | 加授权期限时 |
| feat[] | 功能列表 | 多版本区分 |
| cid | 客户ID | 统计 / 支持系统 |
| sig2 | 第二算法签名 | 双重验证（过度防护需要时） |

### 9.13 快速校验手册（内部）
1. 签名器 sign 输出 activationCode 能被客户端 activate 成功。  
2. 改动本地 activation_v1 JSON 中 hmac 任一字符 → checkActivated 返回 false。  
3. 换电脑（复制 activation_v1）→ checkActivated 返回 false（dLock 不匹配）。  
4. 删除 install-guid 文件 → 设备码改变，旧激活失效（合理预期）。  

---

## 10. 结论（重构版）
在保留“离线 + 单次 + 非对称签名”核心思路下，通过：精简稳定指纹、标准化 base64url、引入 dLock + HMAC 防篡改、预留密钥轮换字段以及多层混淆入口，显著提升了随意复制与简单伪造的门槛，并保持实现复杂度在可控范围。后续可按需要逐步引入功能期限制、特性授权与高级加固（WASM/多公钥/黑名单差分包）。

> 若还需要：A) manifest 完整性方案细节 B) 离线黑名单增量包格式 C) 设备指纹相似度宽容策略，可继续提出。

## 11. 多公钥轮换实现示例

### 11.1 签名器支持多私钥
- 新增 keys/metadata.json 记录当前使用 keyVersion。
- 旧私钥仍可保留但不再用于新签发。

```json
// tools/signer/keys/metadata.json
{ "activeKeyVersion": 2 }
```
```typescript
// tools/signer/src/keyRegistry.ts
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
interface KeyMeta { activeKeyVersion:number; }
const META_FILE = join('keys','metadata.json');
export function getActiveKeyVersion():number {
  if(!existsSync(META_FILE)) return 1;
  return JSON.parse(readFileSync(META_FILE,'utf8')).activeKeyVersion;
}
export function setActiveKeyVersion(v:number){
  writeFileSync(META_FILE, JSON.stringify({activeKeyVersion:v},null,2));
}
```
```typescript
// tools/signer/src/signer.ts 片段（替换 kv:1 行）
import { getActiveKeyVersion } from './keyRegistry';
// ...existing code...
  const kv = getActiveKeyVersion();
  const payload: LicensePayload = { v:1, kv, d: deviceHashFromCode(deviceCode), sid: toB64u(randomBytes(16)), ia: Date.now() };
// ...existing code...
```

### 11.2 客户端多公钥映射
```typescript
// src/renderer/utils/publicKeys.ts
// 分片 + 乱序 + 伪填充（示意）
const KMAP_RAW = [
  { v:1, p:['-----BEGIN PUB','LIC KEY-----','\nMIIBI...旧...','\n-----END PUBLIC KEY-----'] },
  { v:2, p:['-----BEGIN PUB','LIC KEY-----','\nMIIBI...新...','\n-----END PUBLIC KEY-----'] }
];
export function getPublicKey(ver:number):string|undefined {
  const item = KMAP_RAW.find(i=>i.v===ver); if(!item) return undefined;
  return item.p.join('');
}
```
```typescript
// licenseValidator.ts 中 verifySig 前：
import { getPublicKey } from './publicKeys';
// parse 后：
const pub = getPublicKey(parsed.payload.kv);
if(!pub) return false; // 未知版本
if(!verifySigWithKey(parsed.p64, parsed.s64, pub)) return false;
```

### 11.3 轮换流程
1. 生成新密钥对，指定版本号 2。  
2. 添加到 publicKeys.ts，保留旧 v1。  
3. 更新 metadata.json activeKeyVersion=2。  
4. 发布客户端（同时包含 v1,v2）。  
5. 新激活码 kv=2；旧 kv=1 仍验证通过。  
6. 统计（可选）：记录激活日志里 kv 分布，低量后移除 v1。  

## 12. WASM 校验模块模板
目的：将关键哈希 / HMAC / 轻量组合逻辑放入 WASM，增加逆向成本。

### 12.1 Rust 示例（仅演示 SHA-256 + HMAC 包装）
```rust
// license_core/src/lib.rs
use sha2::{Sha256, Digest};
use hmac::{Hmac, Mac};
use wasm_bindgen::prelude::*;

type HmacSha256 = Hmac<Sha256>;

#[wasm_bindgen]
pub fn sha256_hex(data: &[u8]) -> String {
    let mut hasher = Sha256::new();
    hasher.update(data);
    hex::encode(hasher.finalize())
}

#[wasm_bindgen]
pub fn hmac_hex(key: &[u8], data: &[u8]) -> String {
    let mut mac = HmacSha256::new_from_slice(key).unwrap();
    mac.update(data);
    hex::encode(mac.finalize().into_bytes())
}
```
构建：`wasm-pack build --release --target bundler` → dist 包含 wasm + JS 绑定。 

### 12.2 JS 集成
```typescript
// src/renderer/utils/wasmBridge.ts
let wasmReady: Promise<any> | null = null;
export async function loadCore(){
  if(!wasmReady){
    wasmReady = import('../../wasm/license_core').then(m=>m);
  }
  return wasmReady;
}
```
```typescript
// licenseValidator 替换本地 sha256Hex / hmacHex
import { loadCore } from './wasmBridge';
async function sha256HexW(data:string){ const m = await loadCore(); return m.sha256_hex(Buffer.from(data,'utf8')); }
```
说明：实际 RSA 验签仍走 Node crypto（或 WebCrypto），WASM 主要承担被频繁调用且易修改的逻辑（例如 dLock / HMAC），增加修改难度。 

### 12.3 反调试小技巧
- WASM 导出函数名伪装（非语义命名）。
- 在 WASM 内嵌入伪函数（不被调用）混淆控制流。 
- 对返回值追加简单变换（JS 层逆变换），混淆数据流。 

## 13. 公钥 / HMAC Key 混淆拆分策略

### 13.1 基本分片 + 拼接
```typescript
const S = ['R2','p@','m#','x!','7q','Z_','b9','A1'];
// 运行时扰动再拼接
function buildKey(){
  const order = [7,6,5,4,3,2,1,0];
  return Buffer.from(order.map(i=>S[i]).join(''),'utf8');
}
```

### 13.2 XOR 分片
```typescript
// 原 key => bytes K
// 拆成 random R 与 (K^R)
const R = Uint8Array.from([12,99,5,...]);
const KR = Uint8Array.from([K[i]^R[i] for i]);
function recover(){
  const out = Buffer.alloc(R.length);
  for(let i=0;i<R.length;i++) out[i] = R[i]^KR[i];
  return out; // 即原始 key
}
```

### 13.3 多层包装
- 第一层：静态分片数组。  
- 第二层：动态 order 由一个轻度算术函数生成（输入常量种子）。  
- 第三层：首次调用后自删除中间数组引用（delete / 置 null）。  
- 第四层（可选）：把 recover 逻辑放入 WASM。  

### 13.4 伪装与干扰
- 添加多组无效 key 分片（随机生成同长度）。  
- 对真实 key 分片变量名与无效分片混淆命名（a1,a2,...）。  
- 将公钥正文打散为 64~96 长度片段 + 乱序索引 + 校验位。  

## 14. 攻击面 & 缓解映射（速览）
| 攻击向量 | 风险 | 缓解措施 |
|----------|------|----------|
| 复制 activation 存档 | 跨设备使用 | dLock 绑定 + 指纹再哈希 |
| 直接修改存档 JSON | 伪造激活 | HMAC(lic+dLock) + 混淆 key |
| 伪造设备指纹 (VM) | 虚拟化绕过 | 精简稳定指纹 + 运营策略识别异常批量请求 |
| Patch 验证逻辑 | 绕过校验 | 拆分多模块 + WASM + 混淆 + 冗余校验路径 |
| 替换公钥 | 伪造签名 | 公钥分片 + 完整性自检（后续扩展） |
| 旧密钥泄露 | 新发放受影响 | keyVersion + 快速轮换 + 客户端多公钥 |
| 回滚时间/环境 | 利用未来过期机制（若添加） | 使用 monotonic 记录 / 不依赖系统时间（当前无过期字段） |

## 15. 回归与测试清单
| 编号 | 场景 | 期望 |
|------|------|------|
| T1 | 首次获取设备码 | 展示 5-5-5-5-5 格式一致 |
| T2 | 生成激活码并激活 | 返回成功，写入 activation_v1 |
| T3 | 重启应用 | checkActivated=true |
| T4 | 修改存档 hmac | 校验失败，视为未激活 |
| T5 | 修改存档 dLock | 校验失败 |
| T6 | 复制存档到另一机器 | 校验失败 |
| T7 | 生成新 keyVersion=2 激活码 | v2 正常激活 |
| T8 | 旧 v1 激活码仍可用 | 验证通过 |
| T9 | 删除 install-guid 后旧激活 | 失效（预期） |
| T10 | WASM 模块缺失 (破坏) | 回退或警示（根据策略） |

## 16. 渐进式加固路线图
阶段1: 基础重构 (设备码/新 payload/HMAC)。  
阶段2: 多公钥 + 分片混淆。  
阶段3: WASM 引入 + 冗余校验路径。  
阶段4: 完整性清单签名 (manifest+hash)。  
阶段5: 可选功能授权字段 (feat[]) 与时间控制。  

---

> 若还需要：A) manifest 完整性方案细节 B) 离线黑名单增量包格式 C) 设备指纹相似度宽容策略，可继续提出。