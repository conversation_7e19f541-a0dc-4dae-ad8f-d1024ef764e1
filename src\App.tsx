import { useEffect, useRef, useState } from 'react'
import DeviceCodeModal from './components/DeviceCodeModal'

export default function App() {
  const canvasRef = useRef<HTMLCanvasElement | null>(null)
  const [showDeviceCode, setShowDeviceCode] = useState(false)

  // 添加调试日志
  useEffect(() => {
    console.log('App: showDeviceCode state changed to:', showDeviceCode);
  }, [showDeviceCode]);

  useEffect(() => {
    const canvas = canvasRef.current!
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    if (!gl) return

    function resizeCanvas() {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
      gl.viewport(0, 0, canvas.width, canvas.height)
    }
    window.addEventListener('resize', resizeCanvas)
    resizeCanvas()

    const vertexShaderSource = `
      attribute vec2 a_position;
      void main() {
        gl_Position = vec4(a_position, 0.0, 1.0);
      }
    `

    const fragmentShaderSource = `
      precision mediump float;
      uniform float u_time;
      uniform vec2 u_resolution;

      vec3 aurora(vec2 uv, float time) {
        vec2 p = uv - 0.5;
        p.y += 0.3;
        float wave1 = sin(p.x * 3.0 + time * 0.5) * 0.08;
        float wave2 = sin(p.x * 5.0 + time * 0.7 + sin(time * 0.3) * 2.0) * 0.04;
        float wave3 = sin(p.x * 7.0 + time * 1.1 + cos(time * 0.4) * 1.5) * 0.025;
        float wave4 = sin(p.x * 2.0 + time * 0.3 + sin(time * 0.6) * 3.0) * 0.06;
        float y = p.y - wave1 - wave2 - wave3 - wave4;
        float intensity1 = exp(-abs(y) * 16.0) * 0.375;
        float intensity2 = exp(-abs(y + 0.1) * 24.0) * 0.3;
        float intensity3 = exp(-abs(y - 0.05) * 30.0) * 0.225;
        vec3 color1 = vec3(0.2, 0.5, 1.0) * intensity1; // 蓝色
        vec3 color2 = vec3(1.0, 1.0, 1.0) * intensity2; // 白色
        vec3 color3 = vec3(1.0, 0.5, 0.2) * intensity3; // 橙色
        return color1 + color2 + color3;
      }

      vec3 secondaryAurora(vec2 uv, float time) {
        vec2 p = uv - 0.5;
        p.y += 0.1;
        float wave1 = sin(p.x * 2.0 + time * 0.3 + sin(time * 0.2) * 2.5) * 0.06;
        float wave2 = cos(p.x * 4.0 + time * 0.5 + cos(time * 0.35) * 1.8) * 0.03;
        float y = p.y - wave1 - wave2;
        float intensity = exp(-abs(y) * 12.0) * 0.225;
        return vec3(0.2, 0.6, 1.0) * intensity; // 淡蓝色
      }

      vec3 tertiaryAurora(vec2 uv, float time) {
        vec2 p = uv - 0.5;
        p.y -= 0.2;
        float wave1 = sin(p.x * 1.5 + time * 0.4 + sin(time * 0.25) * 3.0) * 0.07;
        float wave2 = cos(p.x * 3.5 + time * 0.6 + cos(time * 0.45) * 2.2) * 0.035;
        float y = p.y - wave1 - wave2;
        float intensity = exp(-abs(y) * 18.0) * 0.18;
        return vec3(1.0, 0.8, 0.4) * intensity; // 橙黄色
      }

      float noise(vec2 p) {
        return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453);
      }

      void main() {
        vec2 uv = gl_FragCoord.xy / u_resolution.xy;
        vec3 color = vec3(0.03, 0.03, 0.075);
        color += aurora(uv, u_time);
        color += secondaryAurora(uv, u_time + 3.0);
        color += tertiaryAurora(uv, u_time + 1.5);
        vec2 starUv = uv * 120.0;
        vec2 starId = floor(starUv);
        vec2 starFract = fract(starUv);
        float star = noise(starId);
        if (star > 0.985) {
          float starBrightness = (sin(u_time * 1.5 + star * 8.0) * 0.3 + 0.4) * 0.75;
          float starDist = length(starFract - 0.5);
          if (starDist < 0.03) {
            color += vec3(0.8, 0.9, 1.0) * (1.0 - starDist * 30.0) * starBrightness;
          }
        }
        float glow = 1.0 - length(uv - 0.5) * 0.6;
        color += vec3(0.075, 0.15, 0.225) * glow * 0.225;
        color += vec3(0.05, 0.15, 0.3) * glow * 0.225; // 深蓝色背景光晕
        gl_FragColor = vec4(color, 1.0);
      }
    `

    function createShader(gl: WebGLRenderingContext, type: number, source: string) {
      const shader = gl.createShader(type)!
      gl.shaderSource(shader, source)
      gl.compileShader(shader)
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('Shader compilation error:', gl.getShaderInfoLog(shader))
        gl.deleteShader(shader)
        return null
      }
      return shader
    }

    const vertexShader = createShader(gl as WebGLRenderingContext, (gl as WebGLRenderingContext).VERTEX_SHADER, vertexShaderSource)!
    const fragmentShader = createShader(gl as WebGLRenderingContext, (gl as WebGLRenderingContext).FRAGMENT_SHADER, fragmentShaderSource)!

    const program = (gl as WebGLRenderingContext).createProgram()!
    ;(gl as WebGLRenderingContext).attachShader(program, vertexShader)
    ;(gl as WebGLRenderingContext).attachShader(program, fragmentShader)
    ;(gl as WebGLRenderingContext).linkProgram(program)

    if (!(gl as WebGLRenderingContext).getProgramParameter(program, (gl as WebGLRenderingContext).LINK_STATUS)) {
      console.error('Program linking error:', (gl as WebGLRenderingContext).getProgramInfoLog(program))
    }

    const positionAttributeLocation = (gl as WebGLRenderingContext).getAttribLocation(program, 'a_position')
    const timeUniformLocation = (gl as WebGLRenderingContext).getUniformLocation(program, 'u_time')
    const resolutionUniformLocation = (gl as WebGLRenderingContext).getUniformLocation(program, 'u_resolution')

    const positionBuffer = (gl as WebGLRenderingContext).createBuffer()
    ;(gl as WebGLRenderingContext).bindBuffer((gl as WebGLRenderingContext).ARRAY_BUFFER, positionBuffer)
    const positions = new Float32Array([
      -1, -1,
       1, -1,
      -1,  1,
      -1,  1,
       1, -1,
       1,  1,
    ])
    ;(gl as WebGLRenderingContext).bufferData((gl as WebGLRenderingContext).ARRAY_BUFFER, positions, (gl as WebGLRenderingContext).STATIC_DRAW)

    function render(time: number) {
      time *= 0.001
      ;(gl as WebGLRenderingContext).clearColor(0, 0, 0, 1)
      ;(gl as WebGLRenderingContext).clear((gl as WebGLRenderingContext).COLOR_BUFFER_BIT)
      ;(gl as WebGLRenderingContext).useProgram(program)
      ;(gl as WebGLRenderingContext).enableVertexAttribArray(positionAttributeLocation)
      ;(gl as WebGLRenderingContext).bindBuffer((gl as WebGLRenderingContext).ARRAY_BUFFER, positionBuffer)
      ;(gl as WebGLRenderingContext).vertexAttribPointer(positionAttributeLocation, 2, (gl as WebGLRenderingContext).FLOAT, false, 0, 0)
      ;(gl as WebGLRenderingContext).uniform1f(timeUniformLocation, time)
      ;(gl as WebGLRenderingContext).uniform2f(resolutionUniformLocation, canvas.width, canvas.height)
      ;(gl as WebGLRenderingContext).drawArrays((gl as WebGLRenderingContext).TRIANGLES, 0, 6)
      requestAnimationFrame(render)
    }

    requestAnimationFrame(render)

    return () => {
      window.removeEventListener('resize', resizeCanvas)
    }
  }, [])

  return (
    <div className="relative min-h-screen w-screen">
      <canvas id="aurora-canvas" ref={canvasRef} className="fixed inset-0 w-full h-full z-0" />
      <div className="fixed inset-0 flex items-center justify-center p-4 z-10">
        <div className="w-full relative max-w-sm">
          <div className="relative card-border overflow-hidden rounded-2xl flex flex-col animate-float">
            <div className="p-6 pb-0 flex justify-center relative">
              <div className="w-full h-32 rounded-xl gradient-border overflow-hidden relative animate-login-pulse">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="relative">
                    <div className="w-16 h-16 rounded-full glass border-2 border-blue-400/30 flex items-center justify-center mb-4">
                    <img src="/login-logo.png" alt="Logo" className="w-12 h-12" />
                  </div>
                    <div className="absolute -top-2 -right-2">
                      <div className="w-4 h-4 bg-orange-400 rounded-full animate-pulse"></div>
                    </div>
                    <div className="absolute -bottom-1 -left-2">
                      <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                    </div>
                    <div className="absolute top-1/2 -right-4">
                      <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                    </div>
                  </div>
                </div>
                <div className="absolute bottom-2 right-2">
                  <div className="flex items-center glass px-2 py-1 rounded-full border border-orange-400/20">
                    <svg className="w-3 h-3 text-orange-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
                    </svg>
                    <span className="text-xs text-green-300">安全</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">登录</h3>
                <p className="text-white/60 text-sm">访问您的专属沙盘管理系统</p>
              </div>
              <form className="space-y-4">
                <div className="relative">
                  <label className="block text-sm font-medium text-white/80 mb-2">账号</label>
                  <div className="relative">
                    <input
                      type="email"
                      className="input-field w-full px-4 py-3 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-0"
                      placeholder="请输入您的账号"
                      onFocus={(e) => e.currentTarget.classList.add('animate-field-glow')}
                      onBlur={(e) => e.currentTarget.classList.remove('animate-field-glow')}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <svg className="w-4 h-4 text-white/40" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <label className="block text-sm font-medium text-white/80 mb-2">密码</label>
                  <div className="relative">
                    <input
                      type="password"
                      className="input-field w-full px-4 py-3 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-0"
                      placeholder="请输入您的密码"
                      onFocus={(e) => e.currentTarget.classList.add('animate-field-glow')}
                      onBlur={(e) => e.currentTarget.classList.remove('animate-field-glow')}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <svg className="w-4 h-4 text-white/40" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <label className="flex items-center">
                    <input type="checkbox" className="sr-only" />
                    <div className="w-4 h-4 border-2 border-blue-400/50 rounded glass flex items-center justify-center">
                      <svg className="w-3 h-3 text-blue-400 hidden" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="ml-2 text-white/60">记住密码</span>
                  </label>
                  <a href="#" className="text-blue-400 hover:text-blue-300 transition">忘记密码？</a>
                </div>
                <button type="submit" className="w-full login-button text-white font-medium py-3 px-4 rounded-lg transition hover:shadow-lg hover:shadow-blue-500/25 focus:outline-none focus:ring-2 focus:ring-blue-500/50">
                  登录
                </button>
                <div className="text-center my-6">
                  <span className="text-white/60 text-sm">首次登录请点击设备码</span>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <button 
                    type="button"
                    className="glass flex items-center justify-center px-4 py-2 border border-white/20 rounded-lg hover:bg-white/10 transition text-sm text-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      window.open('https://work.weixin.qq.com/kfid/kfc605d7e78dd00133d', '_blank');
                    }}
                  >
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                    </svg>
                    联系客服
                  </button>
                  <button 
                    type="button"
                    className="glass flex items-center justify-center px-4 py-2 border border-white/20 rounded-lg hover:bg-white/10 transition text-sm text-white/60 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Device code button clicked, setting showDeviceCode to true');
                      setShowDeviceCode(true);
                    }}
                  >
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 7H7v2h2V7zm0 4H7v2h2v-2zm0-8c-.55 0-1 .45-1 1v1H7c-.55 0-1 .45-1 1v1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h16c.55 0 1-.45 1-1V8c0-.55-.45-1-1-1h-2V6c0-.55-.45-1-1-1h-1V4c0-.55-.45-1-1-1H9zm6 0h2v1h-2V3zm4 2h2v1h-2V5zM5 9h14v8H5V9z"/>
                      <path d="M11 11h2v2h-2v-2zm4 0h2v2h-2v-2z"/>
                    </svg>
                    设备码
                  </button>
                </div>
                <div className="text-center mt-6">
                  <span className="text-white/60 text-sm">还没有账户？ </span>
                  <a href="#" className="text-blue-400 hover:text-blue-300 transition text-sm font-medium">立即注册</a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      
      {/* 设备码模态框 */}
      <DeviceCodeModal 
        isOpen={showDeviceCode} 
        onClose={() => {
          console.log('DeviceCodeModal onClose called, setting showDeviceCode to false');
          setShowDeviceCode(false);
        }} 
      />
    </div>
  )
}
