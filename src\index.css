:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Aurora Login custom styles */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.gradient-border {
  position: relative;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(255, 255, 255, 0.1), rgba(249, 115, 22, 0.1));
}
.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #3b82f6, #ffffff, #f97316);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.card-border {
  background: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow:
    inset 0 0 30px rgba(59, 130, 246, 0.1),
    inset 0 0 60px rgba(255, 255, 255, 0.05),
    0 0 50px rgba(249, 115, 22, 0.2);
}

.input-field {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(59, 130, 246, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}
.input-field:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(249, 115, 22, 0.6);
  box-shadow: 0 0 25px rgba(249, 115, 22, 0.4);
}

.login-button {
  background: #3b82f6;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: #2563eb;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* Device Code Modal Styles - 重新设计与主界面风格一致 */
.device-code-modal {
  background: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow:
    inset 0 0 30px rgba(59, 130, 246, 0.1),
    inset 0 0 60px rgba(255, 255, 255, 0.05),
    0 0 50px rgba(249, 115, 22, 0.2),
    0 20px 40px rgba(0, 0, 0, 0.3);
}

.device-code-display {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(249, 115, 22, 0.05));
  border: 1px solid transparent;
  position: relative;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

.device-code-display::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, #3b82f6, rgba(255, 255, 255, 0.3), #f97316);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.device-code-text {
  background: linear-gradient(135deg, #3b82f6, #ffffff, #f97316);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
  letter-spacing: 0.15em;
  font-weight: 600;
}

.device-code-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid transparent;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #f97316) padding-box,
              linear-gradient(135deg, #3b82f6, #f97316) border-box;
  mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #fff calc(100% - 3px));
  -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #fff calc(100% - 3px));
  animation: device-code-spin 1.5s linear infinite;
}

/* 设备码相关动画 */
@keyframes device-code-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes device-code-pulse {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(59, 130, 246, 0.3),
      inset 0 0 20px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(249, 115, 22, 0.4),
      inset 0 0 30px rgba(249, 115, 22, 0.1);
  }
}

.device-code-display.animate-pulse {
  animation: device-code-pulse 2s ease-in-out infinite;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes modal-enter {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes login-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(249, 115, 22, 0.4);
  }
}

@keyframes field-glow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
  }
}

.animate-modal-enter {
  animation: modal-enter 0.3s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-login-pulse {
  animation: login-pulse 3s ease-in-out infinite;
}

.animate-field-glow {
  animation: field-glow 0.3s ease-out;
}

/* Override default Vite body layout to match original page */
body {
  display: block;
  min-height: 100vh;
}
