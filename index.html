<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON> Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@300;400;500;600&display=swap" rel="stylesheet">
    <script>
      tailwind.config = {
        theme: {
          extend: {
            fontFamily: { geist: ['Geist', 'sans-serif'] },
            animation: {
              'float': 'float 6s ease-in-out infinite',
              'login-pulse': 'loginPulse 4s ease-in-out infinite',
              'field-glow': 'fieldGlow 2s ease-in-out infinite',
              'button-shine': 'buttonShine 3s ease-in-out infinite',
            },
            keyframes: {
              float: { 
                '0%, 100%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-10px)' }
              },
              loginPulse: {
                '0%, 100%': { transform: 'scale(1)', opacity: 0.8 },
                '50%': { transform: 'scale(1.02)', opacity: 1 }
              },
              fieldGlow: {
                '0%, 100%': { boxShadow: '0 0 20px rgba(79, 70, 229, 0.3)' },
                '50%': { boxShadow: '0 0 30px rgba(139, 92, 246, 0.5)' }
              },
              buttonShine: {
                '0%, 100%': { backgroundPosition: '-200% center' },
                '50%': { backgroundPosition: '200% center' }
              }
            }
          }
        }
      }
    </script>
  </head>
  <body class="bg-zinc-900 text-white overflow-hidden font-geist">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
